import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { fetchSingleUrlContent } from '../_shared/url-content-fetcher.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface BlogScrapingRequest {
  task_ids: string[];
  tasks: Array<{
    id: string;
    platform: string;
    topic_id: string;
    datasource_id: string;
    target_date: string;
    metadata: any;
  }>;
}

interface BlogScrapingResponse {
  success: boolean;
  message: string;
  totalPostsScraped: number;
  tasksProcessed: number;
  taskResults: Array<{
    taskId: string;
    datasourceId: string;
    postsScraped: number;
    success: boolean;
    error?: string;
  }>;
}

interface BlogPost {
  title: string;
  content: string;
  author: string;
  url: string;
  external_id: string;
  published_at: string;
  metadata: any;
}

// Enhance post content by fetching from URL if content is too short
async function enhancePostContent(post: BlogPost, minContentLength: number = 500): Promise<BlogPost> {
  // Skip enhancement if content is already long enough or no URL available
  if (!post.url || post.content.length >= minContentLength) {
    return post;
  }

  console.log(`Content too short (${post.content.length} chars) for "${post.title}", attempting URL fetch...`);

  try {
    const fetchResult = await fetchSingleUrlContent(post.url, {
      maxRetries: 2,
      retryDelay: 1000,
      timeoutMs: 15000,
      userAgent: 'topic-stream-weaver/1.0 Blog Content Enhancer'
    });

    if (fetchResult.success && fetchResult.content && fetchResult.content.length > post.content.length) {
      console.log(`Enhanced content for "${post.title}": ${post.content.length} -> ${fetchResult.content.length} chars`);

      return {
        ...post,
        content: fetchResult.content,
        metadata: {
          ...post.metadata,
          original_rss_content: post.content,
          enhanced_from_url: true,
          enhanced_content_length: fetchResult.content.length,
          fetch_time_ms: fetchResult.fetch_time_ms
        }
      };
    } else {
      console.log(`Failed to enhance content for "${post.title}": ${fetchResult.error || 'No improvement'}`);
      return post;
    }
  } catch (error) {
    console.error(`Error enhancing content for "${post.title}":`, error);
    return post;
  }
}

// Parse RSS feed and extract posts
async function parseRSSFeed(rssUrl: string, config: any): Promise<BlogPost[]> {
  console.log(`[RSS-FETCH] Starting RSS fetch for: ${rssUrl}`);
  console.log(`[RSS-FETCH] Config:`, JSON.stringify(config, null, 2));

  try {
    const response = await fetch(rssUrl, {
      headers: {
        'User-Agent': config.user_agent || 'topic-stream-weaver/1.0 RSS Reader'
      },
      timeout: config.timeout || 15000
    });

    console.log(`[RSS-FETCH] Response status: ${response.status} ${response.statusText}`);
    console.log(`[RSS-FETCH] Response headers:`, Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      throw new Error(`Failed to fetch RSS feed: ${response.status} ${response.statusText}`);
    }

    const rssText = await response.text();
    console.log(`[RSS-FETCH] RSS feed fetched successfully, length: ${rssText.length} characters`);

    // Log first 1000 characters for debugging RSS format
    console.log(`[RSS-FETCH] RSS content preview:\n${rssText.substring(0, 1000)}...`);

    // Parse RSS XML
    const posts = await parseRSSXML(rssText, config);
    console.log(`[RSS-PARSE] Final result: ${posts.length} posts parsed from RSS feed`);

    return posts;
  } catch (error) {
    console.error(`[RSS-FETCH] Error fetching RSS feed ${rssUrl}:`, error);
    throw error;
  }
}

// Parse RSS/Atom XML content
async function parseRSSXML(xmlContent: string, config: any): Promise<BlogPost[]> {
  const posts: BlogPost[] = [];

  try {
    console.log(`[RSS-PARSE] Starting XML parsing, content length: ${xmlContent.length}`);

    // Detect feed format (RSS vs Atom)
    const isAtomFeed = xmlContent.includes('<feed') && xmlContent.includes('xmlns="http://www.w3.org/2005/Atom"');
    console.log(`[RSS-PARSE] Detected feed format: ${isAtomFeed ? 'Atom' : 'RSS'}`);

    let items: string[] = [];
    if (isAtomFeed) {
      // Extract entries between <entry> and </entry> tags for Atom feeds
      const entryRegex = /<entry[^>]*>([\s\S]*?)<\/entry>/gi;
      items = xmlContent.match(entryRegex) || [];
      console.log(`[RSS-PARSE] Found ${items.length} entries in Atom feed`);
    } else {
      // Extract items between <item> and </item> tags for RSS feeds
      const itemRegex = /<item[^>]*>([\s\S]*?)<\/item>/gi;
      items = xmlContent.match(itemRegex) || [];
      console.log(`[RSS-PARSE] Found ${items.length} items in RSS feed`);
    }

    if (items.length === 0) {
      console.warn(`[RSS-PARSE] No items found in feed. Checking for common RSS patterns...`);
      // Log some patterns to help debug
      const hasRssTag = xmlContent.includes('<rss');
      const hasFeedTag = xmlContent.includes('<feed');
      const hasItemTag = xmlContent.includes('<item');
      const hasEntryTag = xmlContent.includes('<entry');
      console.log(`[RSS-PARSE] Feed structure check - RSS tag: ${hasRssTag}, Feed tag: ${hasFeedTag}, Item tag: ${hasItemTag}, Entry tag: ${hasEntryTag}`);
    }

    const maxPosts = config.max_posts_per_crawl || 10;
    const timeFilterHours = config.time_filter_hours || config.date_filter_hours || 24;
    const cutoffTime = new Date(Date.now() - (timeFilterHours * 60 * 60 * 1000));

    // Debug mode can be enabled for testing
    const debugMode = false;

    console.log(`[RSS-PARSE] Processing config - maxPosts: ${maxPosts}, timeFilterHours: ${timeFilterHours}`);
    console.log(`[RSS-PARSE] Time cutoff: ${cutoffTime.toISOString()}`);
    console.log(`[RSS-PARSE] Will process ${Math.min(items.length, maxPosts)} items out of ${items.length} total`);

    let skippedOld = 0;
    let parseErrors = 0;

    for (let i = 0; i < Math.min(items.length, maxPosts); i++) {
      const item = items[i];

      try {
        console.log(`[RSS-PARSE] Processing item ${i + 1}/${Math.min(items.length, maxPosts)}`);
        const post = isAtomFeed ? parseAtomEntry(item) : parseRSSItem(item);
        console.log(`[RSS-PARSE] Parsed post: "${post.title}" (${post.published_at})`);

        // Filter by time if specified (disabled in debug mode)
        if (timeFilterHours > 0 && !debugMode) {
          const postDate = new Date(post.published_at);
          if (postDate < cutoffTime) {
            console.log(`[RSS-PARSE] Skipping old post: "${post.title}" (${postDate.toISOString()}) - older than ${timeFilterHours}h cutoff`);
            skippedOld++;
            continue;
          } else {
            console.log(`[RSS-PARSE] Post passes time filter: "${post.title}" (${postDate.toISOString()})`);
          }
        } else if (debugMode) {
          console.log(`[RSS-PARSE] DEBUG: Skipping time filter for post: "${post.title}" (${new Date(post.published_at).toISOString()})`);
        }

        posts.push(post);
        console.log(`[RSS-PARSE] Added post to results: "${post.title}"`);
      } catch (error) {
        console.error(`[RSS-PARSE] Error parsing ${isAtomFeed ? 'Atom entry' : 'RSS item'} ${i + 1}:`, error);
        console.error(`[RSS-PARSE] Failed item content preview:`, item.substring(0, 200));
        parseErrors++;
        continue;
      }
    }

    console.log(`[RSS-PARSE] Processing summary - Total items: ${items.length}, Processed: ${Math.min(items.length, maxPosts)}, Valid posts: ${posts.length}, Skipped (old): ${skippedOld}, Parse errors: ${parseErrors}`);

    console.log(`[RSS-PARSE] Parsed ${posts.length} valid posts after filtering`);

    // Enhance posts with short content by fetching from URL
    console.log(`[RSS-PARSE] Starting content enhancement for ${posts.length} posts`);
    const enhancedPosts: BlogPost[] = [];
    for (let i = 0; i < posts.length; i++) {
      const post = posts[i];
      console.log(`[RSS-PARSE] Enhancing post ${i + 1}/${posts.length}: "${post.title}"`);
      const enhancedPost = await enhancePostContent(post);
      enhancedPosts.push(enhancedPost);
    }

    const enhancedCount = enhancedPosts.filter(p => p.metadata?.enhanced_from_url).length;
    if (enhancedCount > 0) {
      console.log(`[RSS-PARSE] Enhanced ${enhancedCount} posts with URL content`);
    }

    console.log(`[RSS-PARSE] Final result: ${enhancedPosts.length} posts ready for database insertion`);
    return enhancedPosts;
  } catch (error) {
    console.error('[RSS-PARSE] Error parsing RSS/Atom XML:', error);
    console.error('[RSS-PARSE] XML content length:', xmlContent.length);
    console.error('[RSS-PARSE] XML content preview:', xmlContent.substring(0, 500));
    throw error;
  }
}

// Parse individual Atom entry
function parseAtomEntry(entryXml: string): BlogPost {
  const extractTag = (tagName: string): string => {
    const regex = new RegExp(`<${tagName}[^>]*>([\\s\\S]*?)<\\/${tagName}>`, 'i');
    const match = entryXml.match(regex);
    return match ? match[1].trim() : '';
  };

  const extractAtomLink = (): string => {
    const linkRegex = /<link[^>]*href="([^"]*)"[^>]*>/i;
    const match = entryXml.match(linkRegex);
    return match ? match[1] : '';
  };

  const extractCDATA = (content: string): string => {
    const cdataRegex = /<!\[CDATA\[([\s\S]*?)\]\]>/;
    const match = content.match(cdataRegex);
    return match ? match[1] : content;
  };

  // Extract fields from Atom entry
  const title = extractCDATA(extractTag('title'));
  const content = extractCDATA(extractTag('content'));
  const summary = extractCDATA(extractTag('summary'));
  const link = extractAtomLink();
  const updated = extractTag('updated');
  const id = extractTag('id');
  const author = extractTag('author') || extractTag('name'); // Author might be nested in <author><name>

  // Use content if available, otherwise use summary
  const postContent = content || summary || '';

  // Parse publication date (Atom uses <updated>)
  let publishedAt: string;
  try {
    publishedAt = new Date(updated).toISOString();
  } catch (error) {
    console.warn(`Invalid date format: ${updated}, using current time`);
    publishedAt = new Date().toISOString();
  }

  // Clean HTML tags from content
  const cleanContent = postContent
    .replace(/<[^>]*>/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();

  // Generate external_id from Atom id or link
  const externalId = id || link || `atom-${Date.now()}`;

  return {
    title: title || 'Untitled',
    content: cleanContent,
    author: author,
    url: link,
    external_id: externalId,
    published_at: publishedAt,
    metadata: {
      original_content: content,
      original_summary: summary,
      updated: updated,
      atom_id: id,
      content_length: cleanContent.length
    }
  };
}

// Parse individual RSS item
function parseRSSItem(itemXml: string): BlogPost {
  console.log(`[RSS-ITEM] Parsing RSS item, length: ${itemXml.length}`);

  const extractTag = (tagName: string): string => {
    const regex = new RegExp(`<${tagName}[^>]*>([\\s\\S]*?)<\\/${tagName}>`, 'i');
    const match = itemXml.match(regex);
    const result = match ? match[1].trim() : '';
    console.log(`[RSS-ITEM] Extracted ${tagName}: ${result ? `"${result.substring(0, 100)}${result.length > 100 ? '...' : ''}"` : '(empty)'}`);
    return result;
  };

  const extractCDATA = (content: string): string => {
    const cdataRegex = /<!\[CDATA\[([\s\S]*?)\]\]>/;
    const match = content.match(cdataRegex);
    return match ? match[1] : content;
  };

  try {
    const title = extractCDATA(extractTag('title'));
    const description = extractCDATA(extractTag('description'));
    const content = extractCDATA(extractTag('content:encoded')) || description;
    const link = extractTag('link');
    const guid = extractTag('guid');
    const author = extractTag('dc:creator') || extractTag('author') || 'Unknown';
    const pubDate = extractTag('pubDate') || extractTag('dc:date') || new Date().toISOString();

    console.log(`[RSS-ITEM] Parsed basic fields - title: "${title}", link: "${link}", pubDate: "${pubDate}"`);

    if (!title && !link) {
      console.warn(`[RSS-ITEM] Warning: RSS item missing both title and link`);
      console.warn(`[RSS-ITEM] Item XML preview:`, itemXml.substring(0, 300));
    }

    // Generate unique external_id (priority: guid > link > fallback)
    let externalId = guid || link;
    if (!externalId) {
      // Use title + timestamp as fallback
      const titleHash = btoa(title + pubDate).replace(/[^a-zA-Z0-9]/g, '').substring(0, 20);
      externalId = `blog_${titleHash}_${Date.now()}`;
    }

    // Parse publication date
    let publishedAt: string;
    try {
      publishedAt = new Date(pubDate).toISOString();
      console.log(`[RSS-ITEM] Parsed date: ${pubDate} -> ${publishedAt}`);
    } catch (error) {
      console.warn(`[RSS-ITEM] Invalid date format: ${pubDate}, using current time`);
      publishedAt = new Date().toISOString();
    }

    // Clean HTML tags from content
    const cleanContent = content
      .replace(/<[^>]*>/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();

    console.log(`[RSS-ITEM] Final parsed item - title: "${title}", content length: ${cleanContent.length}, external_id: "${externalId}"`);

    return {
      title: title || 'Untitled',
      content: cleanContent,
      author: author,
      url: link,
      external_id: externalId,
      published_at: publishedAt,
      metadata: {
        original_description: description,
        pub_date: pubDate,
        rss_guid: guid,
        content_length: cleanContent.length
      }
    };
  } catch (error) {
    console.error(`[RSS-ITEM] Error parsing RSS item:`, error);
    console.error(`[RSS-ITEM] Item XML:`, itemXml.substring(0, 500));
    throw error;
  }
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  )

  try {
    const requestData: BlogScrapingRequest = await req.json()
    console.log('Blog Scraper: Received request:', JSON.stringify(requestData, null, 2))

    const { task_ids, tasks } = requestData
    let totalPostsScraped = 0

    // Process each task in parallel
    const taskProcessingPromises = tasks.map(async (task) => {
      console.log(`[TASK-${task.id}] Starting blog scraping for datasource ${task.datasource_id}`)

      try {
        // Get datasource details
        const { data: datasource, error: datasourceError } = await supabaseClient
          .from('datasources')
          .select('source_url, source_name, config')
          .eq('id', task.datasource_id)
          .single()

        if (datasourceError || !datasource) {
          throw new Error(`Failed to fetch datasource: ${datasourceError?.message || 'Not found'}`)
        }

        console.log(`[TASK-${task.id}] Found datasource: "${datasource.source_name}" - ${datasource.source_url}`)
        console.log(`[TASK-${task.id}] Datasource config:`, JSON.stringify(datasource.config, null, 2))

        // Scrape RSS feed
        const posts = await parseRSSFeed(datasource.source_url, datasource.config || {})

        if (posts.length === 0) {
          console.log(`[TASK-${task.id}] No posts found for "${datasource.source_name}" - marking task as complete with 0 posts`)

          // Update task status
          await supabaseClient
            .from('processing_tasks')
            .update({
              scrape_status: 'complete',
              posts_scraped: 0,
              completed_at: new Date().toISOString(),
              error_message: null
            })
            .eq('id', task.id)

          return {
            taskId: task.id,
            datasourceId: task.datasource_id,
            postsScraped: 0,
            success: true
          }
        }

        // URL-level deduplication: Check if any of these post URLs have already been scraped
        const postUrls = posts.map(post => post.url).filter(url => url);
        let postsToProcess = posts;

        if (postUrls.length > 0) {
          const { data: existingPosts, error: urlCheckError } = await supabaseClient
            .from('posts')
            .select('id, url, external_id')
            .in('url', postUrls)
            .gte('created_at', new Date(Date.now() - (7 * 24 * 60 * 60 * 1000)).toISOString()) // Check last 7 days

          if (urlCheckError) {
            console.warn(`Warning: Failed to check URL duplicates: ${urlCheckError.message}`)
          } else if (existingPosts && existingPosts.length > 0) {
            // Check for URL overlaps
            const existingUrls = new Set(existingPosts.map(post => post.url));

            // Filter out posts that have already been scraped by URL
            const newPosts = posts.filter(post => !existingUrls.has(post.url));
            const duplicateUrlCount = posts.length - newPosts.length;

            if (duplicateUrlCount > 0) {
              console.log(`Blog Scraper: Found ${duplicateUrlCount} duplicate URLs, processing ${newPosts.length} new posts for ${datasource.source_name}`);
            }

            postsToProcess = newPosts;
          }
        }

        console.log(`Blog Scraper: Processing ${postsToProcess.length} posts (${posts.length - postsToProcess.length} duplicates filtered) for ${datasource.source_name}`)

        if (postsToProcess.length === 0) {
          console.log(`Blog Scraper: All posts for ${datasource.source_name} have already been scraped, skipping`)

          await supabaseClient
            .from('processing_tasks')
            .update({
              scrape_status: 'complete',
              posts_scraped: 0,
              completed_at: new Date().toISOString(),
              error_message: null
            })
            .eq('id', task.id)

          return {
            taskId: task.id,
            datasourceId: task.datasource_id,
            postsScraped: 0,
            success: true,
            message: 'All posts already scraped'
          }
        }

        // Save posts to database
        const postsToInsert = postsToProcess.map(post => ({
          datasource_id: task.datasource_id,
          external_id: post.external_id,
          title: post.title,
          content: post.content,
          author: post.author,
          url: post.url,
          published_at: post.published_at,
          metadata: post.metadata
        }))

        const { data: insertedPosts, error: insertError } = await supabaseClient
          .from('posts')
          .upsert(postsToInsert, {
            onConflict: 'datasource_id,external_id',
            ignoreDuplicates: true
          })
          .select()

        if (insertError) {
          throw new Error(`Failed to insert posts: ${insertError.message}`)
        }

        const actualPostsInserted = insertedPosts?.length || 0

        console.log(`Blog Scraper: Inserted ${actualPostsInserted} posts for ${datasource.source_name}`)

        // Update task status
        await supabaseClient
          .from('processing_tasks')
          .update({
            scrape_status: 'complete',
            posts_scraped: actualPostsInserted,
            completed_at: new Date().toISOString(),
            error_message: null
          })
          .eq('id', task.id)

        return {
          taskId: task.id,
          datasourceId: task.datasource_id,
          postsScraped: actualPostsInserted,
          success: true
        }

      } catch (error) {
        console.error(`Blog Scraper: Error processing task ${task.id}:`, error)

        // Update task status with error
        await supabaseClient
          .from('processing_tasks')
          .update({
            scrape_status: 'failed',
            error_message: error.message,
            completed_at: new Date().toISOString()
          })
          .eq('id', task.id)

        return {
          taskId: task.id,
          datasourceId: task.datasource_id,
          postsScraped: 0,
          success: false,
          error: error.message
        }
      }
    })

    // Wait for all tasks to complete
    const taskResults = await Promise.all(taskProcessingPromises)
    totalPostsScraped = taskResults.reduce((sum, result) => sum + result.postsScraped, 0)

    console.log(`Blog Scraper: Completed processing ${tasks.length} tasks, total posts scraped: ${totalPostsScraped}`)

    const response: BlogScrapingResponse = {
      success: true,
      message: `Successfully processed ${tasks.length} blog scraping tasks`,
      totalPostsScraped,
      tasksProcessed: tasks.length,
      taskResults
    }

    return new Response(
      JSON.stringify(response),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Blog Scraper: Error processing request:', error)
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
        totalPostsScraped: 0,
        tasksProcessed: 0,
        taskResults: []
      } as BlogScrapingResponse),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
});
