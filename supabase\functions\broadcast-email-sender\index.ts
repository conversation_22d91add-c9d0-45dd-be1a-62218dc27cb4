import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

/**
 * 在指定时区格式化日期
 * @param date 要格式化的日期
 * @param timezone 时区标识符 (如 'America/Los_Angeles')
 * @param locale 语言区域设置 (如 'zh-CN' 或 'en-US')
 * @returns 格式化后的日期字符串
 */
function formatDateInTimezone(date: Date, timezone: string, locale: string): string {
  try {
    return new Intl.DateTimeFormat(locale, {
      timeZone: timezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }).format(date);
  } catch (error) {
    console.error(`Error formatting date in timezone ${timezone}:`, error);
    // 回退到默认格式
    return new Intl.DateTimeFormat(locale).format(date);
  }
}

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface EmailSubscriptionPreferences {
  enabled: boolean;
  language: 'zh' | 'en';
  topics: string[];
  platforms: string[];
  favorites_only: boolean;
  podcast?: boolean; // Whether to include AI-generated podcast in emails
  subscribed_at?: string;
  unsubscribed_at?: string;
  timezone?: string;
  send_hour?: number; // 0-23, hour in user's timezone when they want to receive emails
}

interface UserProfile {
  id: string;
  email: string;
  preferences?: {
    email_subscription?: EmailSubscriptionPreferences;
  };
}

interface HeadlineData {
  headline: string;
  url: string;
  platform: string;
  source_name: string;
  datasource_language: string;
}

/**
 * Broadcast邮件发送器
 * 使用Resend Broadcast API按语言分组发送邮件
 */
Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('Broadcast Email Sender: Starting broadcast email sending process...');

    // 解析请求体以检查是否强制执行
    let requestData: any = {};
    try {
      if (req.headers.get('content-type')?.includes('application/json')) {
        requestData = await req.json();
      }
    } catch (error) {
      console.log('No JSON body provided, using defaults');
    }

    const isTest = requestData.test === true;
    const forceExecution = requestData.force === true;
    const targetUsers = requestData.target_users; // 可选：指定特定用户列表

    // 检查是否在正确的时间窗口内（Pacific Time 6-8 AM）
    if (!isTest && !forceExecution) {
      const now = new Date();
      const pacificTime = new Date(now.toLocaleString("en-US", {timeZone: "America/Los_Angeles"}));
      const pacificHour = pacificTime.getHours();
      
      console.log(`Broadcast Email Sender: Current Pacific Time: ${pacificTime.toISOString()}, Hour: ${pacificHour}`);
      
      if (pacificHour < 6 || pacificHour >= 8) {
        console.log('Broadcast Email Sender: Not in the correct time window (6-8 AM Pacific Time), skipping');
        return new Response(
          JSON.stringify({
            success: true,
            message: 'Not in the correct time window (6-8 AM Pacific Time), skipping',
            skipped: true
          }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }
    }

    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // 1. 获取启用邮件订阅的用户（如果指定了target_users，则只获取这些用户）
    let usersQuery = supabaseClient
      .from('user_profiles')
      .select('id, preferences')
      .not('preferences->email_subscription->enabled', 'is', null);

    if (targetUsers && Array.isArray(targetUsers) && targetUsers.length > 0) {
      usersQuery = usersQuery.in('id', targetUsers);
      console.log(`Broadcast Email Sender: Targeting specific users: ${targetUsers.join(', ')}`);
    }

    const { data: users, error: usersError } = await usersQuery;

    if (usersError) {
      throw new Error(`Failed to fetch users: ${usersError.message}`);
    }

    // 过滤出真正启用邮件订阅的用户并获取邮箱
    const subscribedUsers: UserProfile[] = [];

    for (const user of users || []) {
      const emailSub = user.preferences?.email_subscription;
      if (emailSub?.enabled) {
        // 获取用户邮箱
        const { data: authUser, error: authError } = await supabaseClient.auth.admin.getUserById(user.id);
        if (!authError && authUser.user?.email) {
          subscribedUsers.push({
            id: user.id,
            email: authUser.user.email,
            preferences: user.preferences
          });
        }
      }
    }

    console.log(`Broadcast Email Sender: Found ${subscribedUsers.length} subscribed users`);

    if (subscribedUsers.length === 0) {
      console.log('Broadcast Email Sender: No subscribed users found');
      return new Response(
        JSON.stringify({
          success: true,
          message: 'No subscribed users found',
          emailsSent: 0,
          totalUsers: 0
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // 2. 获取过去24小时的headlines - 分别获取中文和英文
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();

    // 获取中文headlines（来自中文+英文数据源）
    const { data: zhHeadlines, error: zhHeadlinesError } = await supabaseClient
      .from('summaries')
      .select(`
        headline,
        source_urls,
        language,
        metadata,
        posts(
          url,
          datasources(
            id,
            platform,
            source_name,
            language,
            topic_id,
            topics(id, name)
          )
        )
      `)
      .not('headline', 'is', null)
      .eq('language', 'ZH')  // 中文headline
      .gte('created_at', twentyFourHoursAgo)
      .order('created_at', { ascending: false });

    // 获取英文headlines（只来自英文数据源）
    const { data: enHeadlines, error: enHeadlinesError } = await supabaseClient
      .from('summaries')
      .select(`
        headline,
        source_urls,
        language,
        metadata,
        posts(
          url,
          datasources(
            id,
            platform,
            source_name,
            language,
            topic_id,
            topics(id, name)
          )
        )
      `)
      .not('headline', 'is', null)
      .eq('language', 'EN')  // 英文headline
      .gte('created_at', twentyFourHoursAgo)
      .order('created_at', { ascending: false });

    if (zhHeadlinesError) {
      throw new Error(`Failed to fetch ZH headlines: ${zhHeadlinesError.message}`);
    }

    if (enHeadlinesError) {
      throw new Error(`Failed to fetch EN headlines: ${enHeadlinesError.message}`);
    }

    console.log(`Broadcast Email Sender: Found ${zhHeadlines?.length || 0} ZH headlines and ${enHeadlines?.length || 0} EN headlines`);

    if ((!zhHeadlines || zhHeadlines.length === 0) && (!enHeadlines || enHeadlines.length === 0)) {
      console.log('Broadcast Email Sender: No headlines found for the past 24 hours');
      return new Response(
        JSON.stringify({
          success: true,
          message: 'No headlines found for the past 24 hours',
          emailsSent: 0,
          totalUsers: subscribedUsers.length
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // 3. 为每个用户发送个性化邮件（使用broadcast模板但保持个性化）
    let emailsSent = 0;
    const errors: string[] = [];

    for (const user of subscribedUsers) {
      try {
        const emailSent = await sendPersonalizedEmail(user, { zhHeadlines: zhHeadlines || [], enHeadlines: enHeadlines || [] }, supabaseClient);
        if (emailSent) emailsSent++;
      } catch (error) {
        console.error(`Failed to send email to ${user.email}:`, error);
        errors.push(`${user.email}: ${error.message}`);
      }
    }

    const results = {
      totalSent: emailsSent,
      errors
    };

    console.log(`Broadcast Email Sender: Successfully sent ${results.totalSent} broadcast emails`);

    return new Response(
      JSON.stringify({
        success: true,
        message: `Successfully sent ${results.totalSent} broadcast emails`,
        emailsSent: results.totalSent,
        totalUsers: subscribedUsers.length,
        errors: results.errors.length > 0 ? results.errors : undefined
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('Broadcast Email Sender: Error:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    );
  }
});

/**
 * 为单个用户发送个性化邮件
 */
async function sendPersonalizedEmail(
  user: UserProfile,
  headlinesData: { zhHeadlines: any[], enHeadlines: any[] },
  supabaseClient: any
): Promise<boolean> {
  const preferences = user.preferences?.email_subscription;
  if (!preferences) return false;

  // 防重复发送的逻辑已经在 email-coordinator 中处理，这里直接处理邮件发送
  const userTimezone = preferences.timezone || 'America/Los_Angeles';

  // 根据用户语言偏好选择headlines
  let filteredHeadlines: any[] = [];

  if (preferences.language === 'zh') {
    // 中文用户：获取所有数据源的中文摘要
    const { data: allDatasources } = await supabaseClient
      .from('datasources')
      .select('id, platform, source_name, language, topic_id, topics(id, name)')
      .eq('active', true);

    if (allDatasources && allDatasources.length > 0) {
      const allDatasourceIds = allDatasources.map((d: any) => d.id);
      const allDatasourceNames = allDatasources.map((d: any) => d.source_name);

      // 过滤中文摘要：使用metadata判断数据源，支持single post和multi post摘要
      filteredHeadlines = headlinesData.zhHeadlines.filter(h => {
        const datasourceId = h.metadata?.datasource_id;
        const sourceName = h.metadata?.source_name;

        // 通过datasource_id或source_name匹配所有数据源
        const idMatch = datasourceId && allDatasourceIds.includes(datasourceId);
        const nameMatch = sourceName && allDatasourceNames.includes(sourceName);

        return idMatch || nameMatch;
      });
    } else {
      // 如果没有数据源，则不发送任何摘要
      filteredHeadlines = [];
    }
  } else {
    // 英文用户：只获取英文数据源的英文摘要
    const { data: enDatasources } = await supabaseClient
      .from('datasources')
      .select('id, platform, source_name, language, topic_id, topics(id, name)')
      .eq('active', true)
      .eq('language', 'EN');

    if (enDatasources && enDatasources.length > 0) {
      const enDatasourceIds = enDatasources.map((d: any) => d.id);
      const enDatasourceNames = enDatasources.map((d: any) => d.source_name);

      // 过滤英文摘要：使用metadata判断数据源语言，支持single post和multi post摘要
      filteredHeadlines = headlinesData.enHeadlines.filter(h => {
        const datasourceId = h.metadata?.datasource_id;
        const sourceName = h.metadata?.source_name;

        // 通过datasource_id或source_name匹配英文数据源
        const idMatch = datasourceId && enDatasourceIds.includes(datasourceId);
        const nameMatch = sourceName && enDatasourceNames.includes(sourceName);

        return idMatch || nameMatch;
      });
    } else {
      // 如果没有英文数据源，则不发送任何摘要
      filteredHeadlines = [];
    }
  }

  // Topic过滤 - 支持两种类型的摘要
  if (preferences.topics && preferences.topics.length > 0) {
    const { data: topicsData } = await supabaseClient
      .from('topics')
      .select('id, name');

    const topicNamesMap = new Map(topicsData?.map((t: any) => [t.name, t.id]) || []);

    filteredHeadlines = filteredHeadlines.filter(h => {
      // 对于有post_id的摘要，从posts.datasources获取topic_id
      const postTopicId = h.posts?.datasources?.topic_id;
      if (postTopicId && preferences.topics!.includes(postTopicId)) {
        return true;
      }

      // 对于没有post_id的摘要，从metadata获取topic_name并转换为topic_id
      const metadataTopicName = h.metadata?.topic_name;
      if (metadataTopicName) {
        const topicId = topicNamesMap.get(metadataTopicName);
        return topicId && preferences.topics!.includes(topicId as string);
      }

      return false;
    });
  }

  // Platform过滤 - 支持两种类型的摘要
  if (preferences.platforms && preferences.platforms.length > 0) {
    filteredHeadlines = filteredHeadlines.filter(h => {
      // 对于有post_id的摘要，从posts.datasources获取platform
      const postPlatform = h.posts?.datasources?.platform;
      // 对于没有post_id的摘要，从metadata获取platform
      const metadataPlatform = h.metadata?.platform;

      return preferences.platforms!.includes(postPlatform || metadataPlatform);
    });
  }

  // 收藏数据源过滤 - 支持两种类型的摘要
  if (preferences.favorites_only) {
    const { data: favorites } = await supabaseClient
      .from('user_favorite_datasources')
      .select('datasource_id')
      .eq('user_id', user.id);

    if (favorites && favorites.length > 0) {
      const favoriteIds = favorites.map((f: any) => f.datasource_id);
      filteredHeadlines = filteredHeadlines.filter(h => {
        // 对于有post_id的摘要，从posts.datasources获取id
        const postDatasourceId = h.posts?.datasources?.id;
        // 对于没有post_id的摘要，从metadata获取datasource_id
        const metadataDatasourceId = h.metadata?.datasource_id;

        return favoriteIds.includes(postDatasourceId || metadataDatasourceId);
      });
    } else {
      // 如果用户选择只要收藏但没有收藏任何数据源，则不发送邮件
      return false;
    }
  }

  if (filteredHeadlines.length === 0) {
    console.log(`No relevant headlines for user ${user.email}, skipping`);
    return false;
  }

  // 获取播客音频URL（如果用户启用了播客偏好）
  let podcastAudioUrl: string | null = null;
  try {
    if (preferences.podcast && preferences.topics && preferences.topics.length > 0) {
      const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();

      // 查询用户订阅主题的已完成播客任务
      const { data: podcastTasks, error: podcastError } = await supabaseClient
        .from('podcast_tasks')
        .select('id, topic_id, language, metadata, audio_url')
        .in('topic_id', preferences.topics)
        .eq('language', preferences.language.toUpperCase())
        .eq('status', 'completed')
        .gte('created_at', twentyFourHoursAgo)
        .order('created_at', { ascending: false })
        .limit(1);

      if (!podcastError && podcastTasks && podcastTasks.length > 0) {
        const latestTask = podcastTasks[0];

        // 优先使用数据库中的 audio_url 字段，如果不存在则构造路径
        const audioPath = latestTask.audio_url || `${latestTask.id}/final_podcast.mp3`;

        // 获取音频文件的公共URL
        const { data: urlData } = supabaseClient.storage
          .from('podcast-audio')
          .getPublicUrl(audioPath);

        if (urlData?.publicUrl) {
          podcastAudioUrl = urlData.publicUrl;
          console.log(`Found podcast audio for user ${user.email}: ${podcastAudioUrl} (from ${latestTask.audio_url ? 'database' : 'constructed path'})`);
        }
      }
    }
  } catch (error) {
    console.error(`Failed to get podcast audio for user ${user.email}:`, error);
    // 不影响邮件发送，继续处理
  }

  // 生成邮件内容
  const emailHtml = generateEmailHtml(filteredHeadlines, preferences.language, user.id, preferences.timezone, podcastAudioUrl);

  // 根据用户时区格式化日期（复用之前定义的 userTimezone）
  const currentDate = new Date();
  const subject = preferences.language === 'zh'
    ? `📧 FeedMe.Today 每日摘要 - ${formatDateInTimezone(currentDate, userTimezone, 'zh-CN')}`
    : `📧 FeedMe.Today Daily Summary - ${formatDateInTimezone(currentDate, userTimezone, 'en-US')}`;

  // 发送邮件
  return await sendEmail(user.email, subject, emailHtml, user.id, filteredHeadlines.length, preferences);
}

/**
 * 使用Resend API发送邮件
 */
async function sendEmail(to: string, subject: string, html: string, userId?: string, headlinesCount?: number, userPreferences?: any): Promise<boolean> {
  let resendId: string | null = null;
  let status: 'sent' | 'failed' = 'failed';
  let errorMessage: string | null = null;

  try {
    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${Deno.env.get('RESEND_API_KEY')}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        from: '<EMAIL>',
        to: [to],
        subject: subject,
        html: html,
        reply_to: '<EMAIL>'
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      errorMessage = `Resend API error: ${response.status} - ${errorText}`;
      throw new Error(errorMessage);
    }

    const result = await response.json();
    resendId = result.id;
    status = 'sent';
    console.log(`Email sent successfully to ${to}, ID: ${result.id}`);

    return true;

  } catch (error) {
    errorMessage = error.message;
    console.error(`Failed to send email to ${to}:`, error);
    throw error;
  } finally {
    // 记录邮件发送日志（如果有用户ID）
    if (userId) {
      try {
        const supabaseClient = createClient(
          Deno.env.get('SUPABASE_URL') ?? '',
          Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
        );

        await supabaseClient
          .from('email_logs')
          .insert({
            user_id: userId,
            email_address: to,
            email_type: 'daily_summary',
            subject: subject,
            status: status,
            resend_id: resendId,
            error_message: errorMessage,
            headlines_count: headlinesCount || 0,
            user_preferences: userPreferences
          });
      } catch (logError) {
        console.error('Failed to log email send:', logError);
        // 不抛出错误，避免影响主流程
      }
    }
  }
}



/**
 * 生成HTML邮件内容 - 从daily-email-sender复制的完整逻辑
 */
function generateEmailHtml(headlines: any[], language: 'zh' | 'en', userId: string, timezone?: string, podcastAudioUrl?: string | null): string {
  const isZh = language === 'zh';
  const userTimezone = timezone || 'America/Los_Angeles';
  const currentDate = new Date();
  const date = formatDateInTimezone(currentDate, userTimezone, isZh ? 'zh-CN' : 'en-US');
  const unsubscribeUrl = userId === 'broadcast'
    ? `{{{RESEND_UNSUBSCRIBE_URL}}}`
    : `https://feedme.today/unsubscribe?token=${btoa(userId)}`;

  // 按 topic 和 platform 分组
  const groupedHeadlines = groupHeadlinesByTopicAndPlatform(headlines);

  // 生成分组后的内容
  const contentSections = Object.keys(groupedHeadlines)
    .sort() // 按 topic 名称排序
    .map(topicName => {
      const topicData = groupedHeadlines[topicName];

      // 生成 topic 标题
      const topicSection = `
        <div style="margin-bottom: 32px;">
          <h2 style="margin: 24px 0 16px; font-size: 20px; font-weight: 700; color: #1f2937; border-bottom: 2px solid #e5e7eb; padding-bottom: 8px;">
            📂 ${topicName}
          </h2>`;

      // 生成每个 platform 的内容
      const platformSections = Object.keys(topicData)
        .sort() // 按 platform 名称排序
        .map(platform => {
          const headlines = topicData[platform];

          const platformTitle = `
            <h3 style="margin: 16px 0 12px; font-size: 16px; font-weight: 600; color: #374151; display: flex; align-items: center; gap: 8px;">
              ${getPlatformIcon(platform)} ${platform}
            </h3>`;

          const headlineItems = headlines.map((h, index) => generateHeadlineItem(h, index)).join('');

          return `${platformTitle}
            <ul style="margin: 0 0 20px; padding: 0; list-style: none;">
              ${headlineItems}
            </ul>`;
        }).join('');

      return `${topicSection}${platformSections}</div>`;
    }).join('');

  // 播客部分（如果有）
  const podcastSection = podcastAudioUrl ? `
    <div class="podcast-player" style="margin: 0 0 32px; padding: 20px; background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%); border-radius: 12px; border: 1px solid #bae6fd;">
      <h3 style="margin: 0 0 16px; font-size: 18px; font-weight: 600; color: #0c4a6e; display: flex; align-items: center; gap: 8px;">
        🎧 ${isZh ? 'AI 播客摘要' : 'AI Podcast Summary'}
      </h3>
      <p style="margin: 0 0 16px; font-size: 14px; color: #0369a1; line-height: 1.5;">
        ${isZh ? '基于今日内容生成的AI播客，为您提供音频形式的内容摘要。' : 'AI-generated podcast based on today\'s content, providing you with an audio summary.'}
      </p>
      <audio controls style="width: 100%; height: 40px; border-radius: 8px;">
        <source src="${podcastAudioUrl}" type="audio/mpeg">
        ${isZh ? '您的浏览器不支持音频播放。' : 'Your browser does not support audio playback.'}
      </audio>
    </div>
  ` : '';

  return `<!DOCTYPE html>
<html lang="${isZh ? 'zh-CN' : 'en'}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="x-apple-disable-message-reformatting">
    <title>FeedMe.Today - ${isZh ? '每日摘要' : 'Daily Summary'}</title>
    <style>
        /* 响应式设计 */
        .email-container {
            width: 100% !important;
            max-width: 800px !important;
            margin: 0 auto !important;
        }

        /* 音频播放器样式 */
        .podcast-player {
            margin: 0 0 32px !important;
            padding: 20px !important;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%) !important;
            border-radius: 12px !important;
            border: 1px solid #bae6fd !important;
        }

        /* 移动端适配 */
        @media only screen and (max-width: 600px) {
            .email-container {
                width: 100% !important;
                max-width: 100% !important;
                margin: 0 !important;
            }

            .email-header {
                padding: 30px 20px 20px !important;
            }

            .email-content {
                padding: 20px !important;
            }

            .email-footer {
                padding: 20px !important;
            }

            h1 {
                font-size: 24px !important;
            }

            h2 {
                font-size: 18px !important;
            }

            h3 {
                font-size: 16px !important;
            }

            .headline-item {
                margin-bottom: 12px !important;
                padding: 10px !important;
            }

            .source-info {
                flex-direction: column !important;
                align-items: flex-start !important;
                gap: 4px !important;
            }
        }
    </style>
</head>
<body style="margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; background-color: #f8fafc; line-height: 1.6;">

    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 0; padding: 0;">
        <tr>
            <td style="padding: 20px 0;">

                <table class="email-container" role="presentation" cellspacing="0" cellpadding="0" border="0" style="margin: 0 auto; background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); overflow: hidden;">

                    <!-- Header -->
                    <tr>
                        <td class="email-header" style="padding: 40px 30px 30px; text-align: center; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 12px 12px 0 0;">
                            <h1 style="margin: 0; font-size: 32px; font-weight: 700; color: #ffffff; text-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                📧 FeedMe.Today
                            </h1>
                            <p style="margin: 12px 0 8px; font-size: 18px; color: #e0e7ff; font-weight: 500;">
                                ${date} ${isZh ? '每日内容摘要' : 'Daily Content Summary'}
                            </p>
                            <div style="display: inline-block; background-color: rgba(255,255,255,0.2); padding: 6px 12px; border-radius: 20px; font-size: 14px; color: #ffffff; font-weight: 500;">
                                ${headlines.length} ${isZh ? '条精选内容' : 'curated headlines'}
                            </div>
                        </td>
                    </tr>

                    <!-- Content -->
                    <tr>
                        <td class="email-content" style="padding: 30px;">
                            ${podcastSection}
                            ${contentSections}
                        </td>
                    </tr>

                    <!-- Footer -->
                    <tr>
                        <td class="email-footer" style="padding: 30px; background-color: #f8fafc; border-top: 1px solid #e5e7eb; text-align: center;">
                            <p style="margin: 0 0 12px; font-size: 14px; color: #6b7280;">
                                <a href="${unsubscribeUrl}" style="color: #6b7280; text-decoration: none;">${isZh ? '取消订阅' : 'Unsubscribe'}</a>
                                <a href="https://feedme.today/content-summary" style="color: #6b7280; text-decoration: none; margin-left: 12px;">${isZh ? '管理偏好' : 'Manage Preferences'}</a>
                            </p>
                            <p style="margin: 12px 0 0; font-size: 12px; color: #9ca3af;">
                                © 2024 FeedMe.Today - ${isZh ? '智能内容聚合平台' : 'Smart Content Aggregation Platform'}
                            </p>
                        </td>
                    </tr>

                </table>

            </td>
        </tr>
    </table>

</body>
</html>`;
}

/**
 * 按topic和platform分组headlines
 */
function groupHeadlinesByTopicAndPlatform(headlines: any[]): Record<string, Record<string, any[]>> {
  const grouped: Record<string, Record<string, any[]>> = {};

  headlines.forEach(headline => {
    // 获取topic名称
    let topicName = 'Other';
    if (headline.posts?.datasources?.topics?.name) {
      topicName = headline.posts.datasources.topics.name;
    } else if (headline.metadata?.topic_name) {
      topicName = headline.metadata.topic_name;
    }

    // 获取platform名称
    let platform = 'Unknown';
    if (headline.posts?.datasources?.platform) {
      platform = headline.posts.datasources.platform;
    } else if (headline.metadata?.platform) {
      platform = headline.metadata.platform;
    }

    // 初始化分组结构
    if (!grouped[topicName]) {
      grouped[topicName] = {};
    }
    if (!grouped[topicName][platform]) {
      grouped[topicName][platform] = [];
    }

    grouped[topicName][platform].push(headline);
  });

  return grouped;
}

/**
 * 获取平台图标
 */
function getPlatformIcon(platform: string): string {
  const icons: Record<string, string> = {
    'Blog': '📝',
    'RSS': '📡',
    'Reddit': '🔴',
    'Twitter': '🐦',
    'YouTube': '📺',
    'Podcast': '🎧',
    'Rednote': '📕',
    'WeChat': '💬',
    'LinkedIn': '💼'
  };
  return icons[platform] || '📄';
}

/**
 * 生成单个headline项目的HTML
 */
function generateHeadlineItem(headline: any, index: number): string {
  // 获取source_name
  let sourceName = 'Unknown Source';
  if (headline.posts?.datasources?.source_name) {
    sourceName = headline.posts.datasources.source_name;
  } else if (headline.metadata?.source_name) {
    sourceName = headline.metadata.source_name;
  }

  // 获取URL
  let url = '#';
  if (headline.posts?.url) {
    url = headline.posts.url;
  } else if (headline.source_urls && headline.source_urls.length > 0) {
    url = headline.source_urls[0];
  }

  // 处理headline文本，确保链接正确显示
  let formattedHeadline = headline.headline;
  if (url !== '#') {
    // 如果headline中没有链接，添加链接
    if (!formattedHeadline.includes('<a ')) {
      formattedHeadline = `<a href="${url}" style="color: #1f2937; text-decoration: none; border-bottom: 1px solid #e5e7eb; transition: border-color 0.2s;" onmouseover="this.style.borderColor='#3b82f6'" onmouseout="this.style.borderColor='#e5e7eb'">${formattedHeadline}</a>`;
    }
  }

  return `<li class="headline-item" style="margin-bottom: 16px; line-height: 1.6; padding: 12px; background-color: ${index % 2 === 0 ? '#f8fafc' : '#ffffff'}; border-radius: 6px; border-left: 3px solid #3b82f6;">
    <div class="headline-content" style="display: flex; align-items: flex-start; gap: 12px;">
      <div style="flex: 1;">
        <div style="color: #1f2937; font-weight: 600; font-size: 15px; margin-bottom: 6px; line-height: 1.4;">
          ${formattedHeadline}
        </div>
        <div class="source-info" style="font-size: 12px; color: #6b7280; display: flex; align-items: center; justify-content: space-between; gap: 8px;">
          <span style="background-color: #e5e7eb; padding: 2px 6px; border-radius: 4px; font-weight: 500;">${sourceName}</span>
        </div>
      </div>
    </div>
  </li>`;
}
